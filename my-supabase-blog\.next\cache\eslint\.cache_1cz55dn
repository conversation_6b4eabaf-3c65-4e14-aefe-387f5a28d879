[{"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\edit-post-form.tsx": "1", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\manage-posts-client.tsx": "3", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\new-post-form.tsx": "5", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\auth\\signout\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\posts\\[id]\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\admin-actions.tsx": "12", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\back-to-top.tsx": "13", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\cherry-editor.tsx": "14", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\client-theme-toggle.tsx": "15", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\homepage-client.tsx": "16", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-content.tsx": "17", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\commands\\index.tsx": "18", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\drag-bar.tsx": "19", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\preview.tsx": "20", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\textarea.tsx": "21", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\toolbar.tsx": "22", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\context.tsx": "23", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\examples\\advanced-usage.tsx": "24", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\examples\\basic-usage.tsx": "25", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\hooks\\use-theme.ts": "26", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\index.ts": "27", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\markdown-editor.tsx": "28", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\types.ts": "29", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\keyboard.ts": "30", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\markdown.ts": "31", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\text-api.ts": "32", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\validation.ts": "33", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-renderer.tsx": "34", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\newsletter-signup.tsx": "35", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\pagination.tsx": "36", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\post-card.tsx": "37", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\post-date.tsx": "38", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\search-bar.tsx": "39", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx": "40", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\table-of-contents.tsx": "41", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx": "42", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-toggle.tsx": "43", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\auth.ts": "44", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\client.ts": "45", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\middleware.ts": "46", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\server.ts": "47", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\toc.ts": "48", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\utils.ts": "49", "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\types\\database.ts": "50"}, {"size": 10736, "mtime": 1751250704763, "results": "51", "hashOfConfig": "52"}, {"size": 1963, "mtime": 1751174250423, "results": "53", "hashOfConfig": "52"}, {"size": 29546, "mtime": 1751178843692, "results": "54", "hashOfConfig": "52"}, {"size": 3758, "mtime": 1751174980494, "results": "55", "hashOfConfig": "52"}, {"size": 5599, "mtime": 1751250704741, "results": "56", "hashOfConfig": "52"}, {"size": 1492, "mtime": 1751183850927, "results": "57", "hashOfConfig": "52"}, {"size": 308, "mtime": 1751166056094, "results": "58", "hashOfConfig": "52"}, {"size": 1071, "mtime": 1751177854832, "results": "59", "hashOfConfig": "52"}, {"size": 6924, "mtime": 1751172324008, "results": "60", "hashOfConfig": "52"}, {"size": 1151, "mtime": 1751250822875, "results": "61", "hashOfConfig": "52"}, {"size": 10963, "mtime": 1751175833508, "results": "62", "hashOfConfig": "52"}, {"size": 2023, "mtime": 1751172400571, "results": "63", "hashOfConfig": "52"}, {"size": 1188, "mtime": 1751173581994, "results": "64", "hashOfConfig": "52"}, {"size": 2790, "mtime": 1751250704497, "results": "65", "hashOfConfig": "52"}, {"size": 130, "mtime": 1751171413376, "results": "66", "hashOfConfig": "52"}, {"size": 13730, "mtime": 1751183726623, "results": "67", "hashOfConfig": "52"}, {"size": 872, "mtime": 1751250704690, "results": "68", "hashOfConfig": "52"}, {"size": 11648, "mtime": 1751251765270, "results": "69", "hashOfConfig": "52"}, {"size": 4310, "mtime": 1751185720200, "results": "70", "hashOfConfig": "52"}, {"size": 3101, "mtime": 1751252623404, "results": "71", "hashOfConfig": "52"}, {"size": 7688, "mtime": 1751252342791, "results": "72", "hashOfConfig": "52"}, {"size": 12008, "mtime": 1751252651101, "results": "73", "hashOfConfig": "52"}, {"size": 2646, "mtime": 1751252364822, "results": "74", "hashOfConfig": "52"}, {"size": 9578, "mtime": 1751252379510, "results": "75", "hashOfConfig": "52"}, {"size": 5760, "mtime": 1751250343695, "results": "76", "hashOfConfig": "52"}, {"size": 5141, "mtime": 1751186049776, "results": "77", "hashOfConfig": "52"}, {"size": 2039, "mtime": 1751186748268, "results": "78", "hashOfConfig": "52"}, {"size": 14565, "mtime": 1751252700461, "results": "79", "hashOfConfig": "52"}, {"size": 5290, "mtime": 1751252511001, "results": "80", "hashOfConfig": "52"}, {"size": 6499, "mtime": 1751186202051, "results": "81", "hashOfConfig": "52"}, {"size": 8242, "mtime": 1751252547782, "results": "82", "hashOfConfig": "52"}, {"size": 5186, "mtime": 1751185504953, "results": "83", "hashOfConfig": "52"}, {"size": 8812, "mtime": 1751252581206, "results": "84", "hashOfConfig": "52"}, {"size": 1432, "mtime": 1751171117840, "results": "85", "hashOfConfig": "52"}, {"size": 6690, "mtime": 1751178667178, "results": "86", "hashOfConfig": "52"}, {"size": 5740, "mtime": 1751178694568, "results": "87", "hashOfConfig": "52"}, {"size": 7661, "mtime": 1751183799312, "results": "88", "hashOfConfig": "52"}, {"size": 2355, "mtime": 1751181876160, "results": "89", "hashOfConfig": "52"}, {"size": 5040, "mtime": 1751178724029, "results": "90", "hashOfConfig": "52"}, {"size": 3611, "mtime": 1751176425891, "results": "91", "hashOfConfig": "52"}, {"size": 5249, "mtime": 1751175322044, "results": "92", "hashOfConfig": "52"}, {"size": 349, "mtime": 1751171035886, "results": "93", "hashOfConfig": "52"}, {"size": 1539, "mtime": 1751167337117, "results": "94", "hashOfConfig": "52"}, {"size": 1008, "mtime": 1751166035214, "results": "95", "hashOfConfig": "52"}, {"size": 212, "mtime": 1751165729262, "results": "96", "hashOfConfig": "52"}, {"size": 1896, "mtime": 1751165746722, "results": "97", "hashOfConfig": "52"}, {"size": 790, "mtime": 1751166018222, "results": "98", "hashOfConfig": "52"}, {"size": 2786, "mtime": 1751175288918, "results": "99", "hashOfConfig": "52"}, {"size": 5643, "mtime": 1751176356520, "results": "100", "hashOfConfig": "52"}, {"size": 236, "mtime": 1751168129038, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "mrqay8", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\edit-post-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\edit-post\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\manage-posts-client.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\new-post-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\auth\\signout\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\posts\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\admin-actions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\back-to-top.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\cherry-editor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\client-theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\homepage-client.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-content.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\commands\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\drag-bar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\preview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\components\\toolbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\context.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\examples\\advanced-usage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\examples\\basic-usage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\hooks\\use-theme.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\markdown-editor.tsx", [], ["252"], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\keyboard.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\markdown.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\text-api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-editor\\utils\\validation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\markdown-renderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\newsletter-signup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\post-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\post-date.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\search-bar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\table-of-contents.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\toc.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\types\\database.ts", [], [], {"ruleId": "253", "severity": 1, "message": "254", "line": 92, "column": 6, "nodeType": "255", "endLine": 92, "endColumn": 8, "suggestions": "256", "suppressions": "257"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'commands', 'defaultTabEnable', 'dispatch', 'extraCommands', 'fullscreen', 'height', 'highlightEnable', 'preview', 'state.commands', 'state.extraCommands', 'tabSize', and 'value'. Either include them or remove the dependency array.", "ArrayExpression", ["258"], ["259"], {"desc": "260", "fix": "261"}, {"kind": "262", "justification": "263"}, "Update the dependencies array to be: [commands, defaultTabEnable, dispatch, extraCommands, fullscreen, height, highlightEnable, preview, state.commands, state.extraCommands, tabSize, value]", {"range": "264", "text": "265"}, "directive", "", [2868, 2870], "[commands, defaultTabEnable, dispatch, extraCommands, fullscreen, height, highlightEnable, preview, state.commands, state.extraCommands, tabSize, value]"]