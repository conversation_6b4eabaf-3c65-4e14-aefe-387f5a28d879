(()=>{var e={};e.id=936,e.ids=[936],e.modules={232:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),n=t(80173);async function o(){return(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8",children:(0,s.jsx)(n.default,{})})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9783:(e,r,t)=>{Promise.resolve().then(t.bind(t,31747))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18375:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,r)=>{"use strict";function t(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"encodeURIPath",{enumerable:!0,get:function(){return t}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30036:(e,r,t)=>{"use strict";t.d(r,{default:()=>n.a});var s=t(49587),n=t.n(s)},31747:(e,r,t)=>{"use strict";t.d(r,{default:()=>u});var s=t(60687),n=t(43210),o=t(16189),i=t(85814),a=t.n(i),l=t(71365),d=t(79481);function u(){let[e,r]=(0,n.useState)(""),[t,i]=(0,n.useState)(""),[u,c]=(0,n.useState)(!1),[m,p]=(0,n.useState)(""),h=(0,o.useRouter)(),x=async r=>{if(r.preventDefault(),!e.trim()||!t.trim())return void p("Title and content are required");c(!0),p("");try{let r=(0,d.U)(),{error:s}=await r.from("posts").insert([{title:e.trim(),content:t.trim()}]);if(s)throw s;h.push("/"),h.refresh()}catch(e){console.error("Error creating post:",e),p("Error creating post. Please try again.")}finally{c(!1)}};return(0,s.jsx)("div",{className:"animate-fade-in",children:(0,s.jsxs)("form",{onSubmit:x,className:"space-y-8",children:[m&&(0,s.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 text-destructive px-6 py-4 rounded-xl animate-slide-in shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-destructive/10 rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Error"}),(0,s.jsx)("p",{className:"text-sm",children:m})]})]})}),(0,s.jsxs)("div",{className:"bg-card rounded-2xl border border-border shadow-xl overflow-hidden",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-secondary/5 px-6 py-4 border-b border-border flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})}),"Create New Post"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Share your thoughts with the world using our markdown editor"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(a(),{href:"/",className:"bg-secondary text-secondary-foreground px-4 py-2 rounded-lg hover:bg-secondary/80 transition-all duration-200 font-medium shadow-sm hover:shadow-md inline-flex items-center justify-center group text-xs",children:[(0,s.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),"Cancel"]}),(0,s.jsx)("button",{type:"submit",disabled:u||!e.trim()||!t.trim(),className:"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-md hover:shadow-lg inline-flex items-center justify-center group text-xs",children:u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Publishing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})}),"Publish Post"]})})]})]}),(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{htmlFor:"title",className:"flex items-center text-xs font-medium text-foreground",children:[(0,s.jsx)("div",{className:"w-5 h-5 bg-primary/10 rounded-md flex items-center justify-center mr-2",children:(0,s.jsx)("svg",{className:"w-3 h-3 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"})})}),"Post Title",(0,s.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",id:"title",value:e,onChange:e=>r(e.target.value),className:"w-full px-3 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 text-base placeholder:text-muted-foreground/60",placeholder:"Enter an engaging title for your post...",disabled:u}),(0,s.jsxs)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground",children:[e.length,"/100"]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{htmlFor:"content",className:"flex items-center text-xs font-medium text-foreground",children:[(0,s.jsx)("div",{className:"w-5 h-5 bg-primary/10 rounded-md flex items-center justify-center mr-2",children:(0,s.jsx)("svg",{className:"w-3 h-3 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),"Post Content",(0,s.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,s.jsx)("div",{className:"border border-input rounded-lg overflow-hidden shadow-sm bg-background",children:(0,s.jsx)(l.D,{value:t,onChange:i,minHeight:"600px",placeholder:"Start writing your amazing content..."})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground mt-2 px-1",children:[(0,s.jsx)("span",{children:"Markdown supported"}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("span",{children:[t.split(" ").length," words"]}),(0,s.jsxs)("span",{children:[t.length," characters"]}),(0,s.jsxs)("span",{children:[Math.ceil(t.split(" ").length/200)," min read"]})]})]})]})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35247:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},39727:()=>{},47990:()=>{},49587:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n}});let s=t(14985)._(t(64963));function n(e,r){var t;let n={};"function"==typeof e&&(n.loader=e);let o={...n,...r};return(0,s.default)({...o,modules:null==(t=o.loadableGenerated)?void 0:t.modules})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},52331:(e,r,t)=>{Promise.resolve().then(t.bind(t,96871))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56780:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let s=t(81208);function n(e){let{reason:r,children:t}=e;throw Object.defineProperty(new s.BailoutToCSRError(r),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"PreloadChunks",{enumerable:!0,get:function(){return a}});let s=t(60687),n=t(51215),o=t(29294),i=t(19587);function a(e){let{moduleIds:r}=e,t=o.workAsyncStorage.getStore();if(void 0===t)return null;let a=[];if(t.reactLoadableManifest&&r){let e=t.reactLoadableManifest;for(let t of r){if(!e[t])continue;let r=e[t].files;a.push(...r)}}return 0===a.length?null:(0,s.jsx)(s.Fragment,{children:a.map(e=>{let r=t.assetPrefix+"/_next/"+(0,i.encodeURIPath)(e);return e.endsWith(".css")?(0,s.jsx)("link",{precedence:"dynamic",href:r,rel:"stylesheet",as:"style"},e):((0,n.preload)(r,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return d}});let s=t(60687),n=t(43210),o=t(56780),i=t(64777);function a(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},d=function(e){let r={...l,...e},t=(0,n.lazy)(()=>r.loader().then(a)),d=r.loading;function u(e){let a=d?(0,s.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,l=!r.ssr||!!r.loading,u=l?n.Suspense:n.Fragment,c=r.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.PreloadChunks,{moduleIds:r.modules}),(0,s.jsx)(t,{...e})]}):(0,s.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(t,{...e})});return(0,s.jsx)(u,{...l?{fallback:a}:{},children:c})}return u.displayName="LoadableComponent",u}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71365:(e,r,t)=>{"use strict";t.d(r,{D:()=>a});var s=t(60687),n=t(30036),o=t(43210);let i=(0,n.default)(async()=>{},{loadableGenerated:{modules:["components\\cherry-editor.tsx -> @uiw/react-md-editor"]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse min-h-[400px] flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"Loading editor..."})})});function a({value:e="",onChange:r,minHeight:t="500px",placeholder:n="Start writing your amazing content..."}){let[a,l]=(0,o.useState)(!1);if(!a)return(0,s.jsx)("div",{className:"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse flex items-center justify-center",style:{minHeight:t},children:(0,s.jsx)("div",{className:"text-muted-foreground",children:"Loading editor..."})});try{return(0,s.jsx)("div",{className:"w-full md-editor-wrapper",style:{minHeight:t},"data-color-mode":"auto",children:(0,s.jsx)(i,{value:e,onChange:e=>r?.(e||""),preview:"live",hideToolbar:!1,visibleDragbar:!1,height:parseInt(t),textareaProps:{placeholder:n,style:{fontSize:15,lineHeight:1.7,fontFamily:'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',color:"inherit",padding:"16px"}},style:{backgroundColor:"hsl(var(--background))",color:"hsl(var(--foreground))",minHeight:t,width:"100%"}})})}catch(o){return console.error("Error rendering MDEditor:",o),(0,s.jsxs)("div",{className:"w-full p-4 border border-input bg-background text-foreground rounded-md",children:[(0,s.jsx)("div",{className:"text-red-500 mb-2",children:"Error loading editor"}),(0,s.jsx)("textarea",{value:e,onChange:e=>r?.(e.target.value),placeholder:n,className:"w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground",style:{minHeight:`calc(${t} - 2rem)`}})]})}}},72831:(e,r,t)=>{Promise.resolve().then(t.bind(t,80173))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79481:(e,r,t)=>{"use strict";t.d(r,{U:()=>n});var s=t(59522);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},79551:e=>{"use strict";e.exports=require("url")},80173:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\app\\\\admin\\\\new-post\\\\new-post-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\new-post-form.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx","ThemeProvider")},91645:e=>{"use strict";e.exports=require("net")},92083:(e,r,t)=>{Promise.resolve().then(t.bind(t,83701))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,metadata:()=>d});var s=t(37413),n=t(22376),o=t.n(n),i=t(68726),a=t.n(i);t(61135);var l=t(83701);let d={title:"My Blog",description:"A modern blog built with Next.js and Supabase",icons:{icon:"/icon.png"}};function u({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,suppressHydrationWarning:!0,children:(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,children:e})})})}},94735:e=>{"use strict";e.exports=require("events")},96871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});var s=t(60687);t(43210);var n=t(10218);function o({children:e,...r}){return(0,s.jsx)(n.N,{...r,children:e})}},99434:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>d});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["admin",{children:["new-post",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,232)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\new-post\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/new-post/page",pathname:"/admin/new-post",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,98,567,318],()=>t(99434));module.exports=s})();